import { FastifyInstance } from 'fastify';
import { setupTestApp, teardownTestApp, clearDatabase, createTestUser } from '../setup';
import { User } from '../../src/models/user.model';
import bcrypt from 'bcrypt';

describe('Auth Integration Tests', () => {
  let app: FastifyInstance;

  beforeAll(async () => {
    app = await setupTestApp();
  }, 30000);

  afterAll(async () => {
    await teardownTestApp();
  }, 30000);

  beforeEach(async () => {
    await clearDatabase();
  });

  describe('POST /auth/login', () => {
    it('should login user with valid credentials', async () => {
      // Create test user
      const userData = createTestUser({
        email: '<EMAIL>',
        password: await bcrypt.hash('Test123!@#', 10)
      });

      const user = new User(userData);
      await user.save();

      const response = await app.inject({
        method: 'POST',
        url: '/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'Test123!@#'
        }
      });

      expect(response.statusCode).toBe(200);

      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data).toHaveProperty('token');
      expect(body.data).toHaveProperty('user');
      expect(body.data.user.email).toBe('<EMAIL>');

      // Cleanup
      await User.findByIdAndDelete(user._id);
    });

    it('should return 401 for invalid credentials', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'wrongpassword'
        }
      });

      expect(response.statusCode).toBe(401);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.message).toContain('invalid credentials');
    });

    it('should return 400 for missing fields', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/auth/login',
        payload: {
          email: '<EMAIL>'
          // missing password
        }
      });

      expect(response.statusCode).toBe(400);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
    });

    it('should return 400 for invalid email format', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/auth/login',
        payload: {
          email: 'invalid-email',
          password: 'Test123!@#'
        }
      });

      expect(response.statusCode).toBe(400);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
    });
  });

  describe('POST /auth/refresh', () => {
    it('should refresh valid token', async () => {
      // First login to get a token
      const userData = createTestUser({
        email: '<EMAIL>',
        password: await bcrypt.hash('Test123!@#', 10)
      });
      
      const user = new User(userData);
      await user.save();

      const loginResponse = await app.inject({
        method: 'POST',
        url: '/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'Test123!@#'
        }
      });

      const loginBody = JSON.parse(loginResponse.body);
      const token = loginBody.data.token;

      // Now refresh the token
      const refreshResponse = await app.inject({
        method: 'POST',
        url: '/auth/refresh',
        headers: {
          authorization: `Bearer ${token}`
        }
      });

      expect(refreshResponse.statusCode).toBe(200);
      
      const refreshBody = JSON.parse(refreshResponse.body);
      expect(refreshBody.success).toBe(true);
      expect(refreshBody.data).toHaveProperty('token');
      expect(refreshBody.data.token).not.toBe(token);
    });

    it('should return 401 for invalid token', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/auth/refresh',
        headers: {
          authorization: 'Bearer invalid-token'
        }
      });

      expect(response.statusCode).toBe(401);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
    });

    it('should return 401 for missing token', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/auth/refresh'
      });

      expect(response.statusCode).toBe(401);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
    });
  });

  describe('POST /auth/logout', () => {
    it('should logout user successfully', async () => {
      // First login to get a token
      const userData = createTestUser({
        email: '<EMAIL>',
        password: await bcrypt.hash('Test123!@#', 10)
      });
      
      const user = new User(userData);
      await user.save();

      const loginResponse = await app.inject({
        method: 'POST',
        url: '/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'Test123!@#'
        }
      });

      const loginBody = JSON.parse(loginResponse.body);
      const token = loginBody.data.token;

      // Now logout
      const logoutResponse = await app.inject({
        method: 'POST',
        url: '/auth/logout',
        headers: {
          authorization: `Bearer ${token}`
        }
      });

      expect(logoutResponse.statusCode).toBe(200);
      
      const logoutBody = JSON.parse(logoutResponse.body);
      expect(logoutBody.success).toBe(true);
      expect(logoutBody.message).toContain('logged out successfully');
    });

    it('should return 401 for invalid token', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/auth/logout',
        headers: {
          authorization: 'Bearer invalid-token'
        }
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('POST /auth/change-password', () => {
    it('should change password with valid data', async () => {
      const userData = createTestUser({
        email: '<EMAIL>',
        password: await bcrypt.hash('OldPassword123!', 10)
      });
      
      const user = new User(userData);
      await user.save();

      // Login first
      const loginResponse = await app.inject({
        method: 'POST',
        url: '/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'OldPassword123!'
        }
      });

      const loginBody = JSON.parse(loginResponse.body);
      const token = loginBody.data.token;

      // Change password
      const changeResponse = await app.inject({
        method: 'POST',
        url: '/auth/change-password',
        headers: {
          authorization: `Bearer ${token}`
        },
        payload: {
          currentPassword: 'OldPassword123!',
          newPassword: 'NewPassword123!',
          confirmPassword: 'NewPassword123!'
        }
      });

      expect(changeResponse.statusCode).toBe(200);
      
      const changeBody = JSON.parse(changeResponse.body);
      expect(changeBody.success).toBe(true);
      expect(changeBody.message).toContain('password changed successfully');
    });

    it('should return 400 for invalid current password', async () => {
      const userData = createTestUser({
        email: '<EMAIL>',
        password: await bcrypt.hash('OldPassword123!', 10)
      });
      
      const user = new User(userData);
      await user.save();

      // Login first
      const loginResponse = await app.inject({
        method: 'POST',
        url: '/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'OldPassword123!'
        }
      });

      const loginBody = JSON.parse(loginResponse.body);
      const token = loginBody.data.token;

      // Try to change password with wrong current password
      const changeResponse = await app.inject({
        method: 'POST',
        url: '/auth/change-password',
        headers: {
          authorization: `Bearer ${token}`
        },
        payload: {
          currentPassword: 'WrongPassword',
          newPassword: 'NewPassword123!',
          confirmPassword: 'NewPassword123!'
        }
      });

      expect(changeResponse.statusCode).toBe(400);
      
      const changeBody = JSON.parse(changeResponse.body);
      expect(changeBody.success).toBe(false);
      expect(changeBody.message).toContain('current password is incorrect');
    });
  });
});
